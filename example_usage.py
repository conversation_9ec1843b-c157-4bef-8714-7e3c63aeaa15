#!/usr/bin/env python3
"""
Example usage of the Company Research Agent

This script demonstrates how to use the agent programmatically.
"""

from agent import research_company, research_company_async
import asyncio

def example_sync_usage():
    """Example of synchronous usage"""
    print("=== Synchronous Usage Example ===")
    
    company_name = "Google"
    
    # Research the company
    result = research_company(company_name)
    
    if result["success"]:
        print(f"✅ Successfully researched {result['company_name']}")
        print(f"📊 Completed {len(result['completed_searches'])}/8 searches")
        print("\n" + "=" * 50)
        print("REPORT:")
        print("=" * 50)
        print(result["report"])
        
        # Save to file
        filename = f"{company_name.replace(' ', '_')}_report.md"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(result["report"])
        print(f"\n💾 Report saved to {filename}")
        
    else:
        print(f"❌ Research failed: {result['error']}")

async def example_async_usage():
    """Example of asynchronous usage"""
    print("=== Asynchronous Usage Example ===")
    
    companies = ["Microsoft", "Apple", "Amazon"]
    
    # Research multiple companies concurrently
    tasks = [research_company_async(company) for company in companies]
    results = await asyncio.gather(*tasks)
    
    for result in results:
        if result["success"]:
            print(f"✅ {result['company_name']}: {len(result['completed_searches'])}/8 searches completed")
        else:
            print(f"❌ {result['company_name']}: {result['error']}")

def example_batch_research():
    """Example of researching multiple companies"""
    print("=== Batch Research Example ===")
    
    companies = ["Netflix", "Spotify", "Adobe"]
    
    for company in companies:
        print(f"\n🔍 Researching {company}...")
        result = research_company(company)
        
        if result["success"]:
            # Save each report to a separate file
            filename = f"reports/{company.replace(' ', '_')}_report.md"
            os.makedirs("reports", exist_ok=True)
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(result["report"])
            
            print(f"✅ {company} report saved to {filename}")
        else:
            print(f"❌ Failed to research {company}: {result['error']}")

if __name__ == "__main__":
    import os
    
    # Choose which example to run
    print("Choose an example to run:")
    print("1. Sync usage (single company)")
    print("2. Async usage (multiple companies)")
    print("3. Batch research (multiple companies, saved to files)")
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice == "1":
        example_sync_usage()
    elif choice == "2":
        asyncio.run(example_async_usage())
    elif choice == "3":
        example_batch_research()
    else:
        print("Invalid choice. Running sync example...")
        example_sync_usage()