import asyncio
import json
import os
from datetime import datetime
from typing import Annotated, Any, Dict, List, Optional, TypedDict

import httpx
from dotenv import load_dotenv
from langchain_anthropic import Chat<PERSON>nthropic
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langgraph.graph import END, START, StateGraph
from langgraph.graph.message import add_messages

load_dotenv()

# API Configuration
PERPLEXITY_API_KEY = os.getenv("PERPLEXITY_API_KEY", "your_perplexity_api_key_here")
PERPLEXITY_BASE_URL = "https://api.perplexity.ai/chat/completions"
ANTHROPIC_API_KEY = os.getenv("ANTHROPIC_API_KEY", "your_anthropic_api_key_here")

# Initialize Anthropic client
anthropic_model = None
if ANTHROPIC_API_KEY != "your_anthropic_api_key_here":
    anthropic_model = ChatAnthropic(
        model="claude-3-sonnet-20240229",
        api_key=ANTHROPIC_API_KEY,
        temperature=0.7
    )


async def perplexity_ask(
    messages: List[Dict[str, str]], model: str = "sonar", client: Optional[httpx.AsyncClient] = None
) -> Dict[str, str]:
    """
    Perform chat completion using Perplexity API with httpx

    Args:
        messages: List of messages with 'role' and 'content' keys
        model: Model identifier (default: "sonar")
        client: Optional httpx.AsyncClient for connection reuse

    Returns:
        Dict with 'content' key containing the response
    """
    # Create client if not provided
    if client is None:
        async with httpx.AsyncClient() as temp_client:
            return await perplexity_ask(messages, model, temp_client)

    try:
        # Construct request body
        body = {
            "model": model,
            "messages": messages,
            # Additional parameters can be added here if required
            # See Sonar API documentation: https://docs.perplexity.ai/api-reference/chat-completions
        }

        # Make API request
        response = await client.post(
            PERPLEXITY_BASE_URL,
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {PERPLEXITY_API_KEY}",
            },
            json=body,
            timeout=30.0,  # 30 second timeout
        )

        # Check for non-successful HTTP status
        if response.status_code >= 400:
            error_text = response.text
            raise Exception(f"Perplexity API error: {response.status_code} {response.reason_phrase}\n{error_text}")

        # Parse JSON response
        try:
            data = response.json()
        except json.JSONDecodeError as e:
            raise Exception(f"Failed to parse JSON response from Perplexity API: {e}")

        # Extract main message content
        message_content = data.get("choices", [{}])[0].get("message", {}).get("content", "")

        # If citations are provided, append them to the message content
        citations = data.get("citations", [])
        if citations and isinstance(citations, list) and len(citations) > 0:
            message_content += "\n\nCitations:\n"
            for index, citation in enumerate(citations):
                message_content += f"[{index + 1}] {citation}\n"

        return {"content": message_content}

    except httpx.RequestError as e:
        raise Exception(f"Network error while calling Perplexity API: {e}")
    except Exception as e:
        raise Exception(f"Error calling Perplexity API: {e}")


# Định nghĩa State cho Agent  
import operator

def merge_research_results(left: Dict[str, Any], right: Dict[str, Any]) -> Dict[str, Any]:
    """Custom reducer to merge research results dictionaries"""
    result = left.copy() if left else {}
    if right:
        result.update(right)
    return result

class CompanyResearchState(TypedDict):
    """State cho Company Research Agent"""

    messages: Annotated[List[BaseMessage], add_messages]
    company_name: str
    research_results: Annotated[Dict[str, Any], merge_research_results]  # Use custom reducer for concurrent updates
    search_queries: List[str]
    completed_searches: Annotated[List[str], operator.add]  # Use reducer for concurrent updates


async def search_company_basic_info_node(state: CompanyResearchState) -> Dict[str, Any]:
    """Node tìm kiếm thông tin cơ bản về công ty"""
    company_name = state["company_name"]
    try:
        messages = [
            {
                "role": "system",
                "content": "Bạn là chuyên gia nghiên cứu doanh nghiệp. Trả lời bằng tiếng Việt, cung cấp thông tin chính xác và có cấu trúc.",
            },
            {
                "role": "user",
                "content": f"""Tìm thông tin cơ bản về công ty "{company_name}" bao gồm:
                - Lĩnh vực hoạt động chính
                - Quy mô nhân sự
                - Năm thành lập
                - Trụ sở chính
                - Website chính thức
                - Sản phẩm/dịch vụ chính
                - Cấu trúc tổ chức
                
                Vui lòng cung cấp thông tin chính xác và cập nhật nhất.""",
            },
        ]

        response = await perplexity_ask(messages=messages)
        content = response.get("content", "Không thể tìm thấy thông tin cơ bản")
        
        return {
            "research_results": {"basic_info": content},
            "completed_searches": ["basic_info"]
        }

    except Exception as e:
        return {
            "research_results": {"basic_info": f"Lỗi khi tìm kiếm thông tin cơ bản: {str(e)}"},
            "completed_searches": ["basic_info"]
        }


async def search_company_culture_benefits_node(state: CompanyResearchState) -> Dict[str, Any]:
    """Node tìm kiếm thông tin về văn hóa công ty và chế độ đãi ngộ"""
    company_name = state["company_name"]
    try:
        messages = [
            {
                "role": "system",
                "content": "Bạn là chuyên gia HR và workplace culture. Trả lời bằng tiếng Việt với thông tin chi tiết và thực tế.",
            },
            {
                "role": "user",
                "content": f"""Tìm hiểu về văn hóa làm việc và chế độ đãi ngộ tại công ty "{company_name}":
                - Văn hóa công ty (values, mission, vision)
                - Môi trường làm việc
                - Chế độ lương thưởng
                - Phúc lợi nhân viên
                - Work-life balance
                - Chính sách remote/hybrid
                - Chế độ nghỉ phép
                - Bảo hiểm và healthcare
                - Các hoạt động team building
                
                Ưu tiên thông tin từ website chính thức, career pages, và reviews nhân viên.""",
            },
        ]

        response = await perplexity_ask(messages=messages)
        content = response.get("content", "Không thể tìm thấy thông tin văn hóa và đãi ngộ")
        
        return {
            "research_results": {
                "culture_benefits": content
            },
            "completed_searches": ["culture_benefits"]
        }

    except Exception as e:
        return {
            "research_results": {
                "culture_benefits": f"Lỗi khi tìm kiếm thông tin văn hóa: {str(e)}"
            },
            "completed_searches": ["culture_benefits"]
        }


async def search_company_financial_performance_node(state: CompanyResearchState) -> Dict[str, Any]:
    """Node tìm kiếm thông tin tài chính và hiệu quả kinh doanh"""
    company_name = state["company_name"]
    try:
        messages = [
            {
                "role": "system",
                "content": "Bạn là chuyên gia phân tích tài chính doanh nghiệp. Cung cấp thông tin tài chính chính xác bằng tiếng Việt.",
            },
            {
                "role": "user",
                "content": f"""Phân tích tình hình tài chính của công ty "{company_name}":
                - Doanh thu các năm gần đây (2022-2024)
                - Lợi nhuận và tỷ suất sinh lời
                - Tốc độ tăng trưởng
                - Tình hình nợ và thanh khoản
                - Vị thế tài chính trong ngành
                - Đầu tư R&D
                - Triển vọng tài chính
                - Funding rounds (nếu là startup)
                
                Ưu tiên thông tin từ báo cáo tài chính chính thức, press releases, và các nguồn tin đáng tin cậy.""",
            },
        ]

        response = await perplexity_ask(messages=messages)
        content = response.get("content", "Không thể tìm thấy thông tin tài chính")
        
        return {
            "research_results": {
                "financial_performance": content
            },
            "completed_searches": ["financial_performance"]
        }

    except Exception as e:
        return {
            "research_results": {
                "financial_performance": f"Lỗi khi tìm kiếm thông tin tài chính: {str(e)}"
            },
            "completed_searches": ["financial_performance"]
        }


async def search_company_reviews_glassdoor_node(state: CompanyResearchState) -> Dict[str, Any]:
    """Node tìm kiếm đánh giá nhân viên từ Glassdoor, Indeed, và các platform tương tự"""
    company_name = state["company_name"]
    try:
        messages = [
            {
                "role": "system",
                "content": "Bạn là chuyên gia phân tích reviews và employer branding. Tổng hợp đánh giá nhân viên một cách khách quan bằng tiếng Việt.",
            },
            {
                "role": "user",
                "content": f"""Tìm và phân tích đánh giá của nhân viên về công ty "{company_name}" từ:
                - Glassdoor
                - Indeed
                - JobStreet
                - VietnamWorks
                - Các forum và mạng xã hội
                
                Tập trung vào:
                - Overall rating và breakdown theo categories
                - Ưu điểm được nhắc đến nhiều nhất
                - Nhược điểm phổ biến
                - Đánh giá về management và leadership
                - Career growth opportunities
                - Compensation satisfaction
                - Work environment và culture
                - Reviews từ fresh graduates/entry level
                
                Cung cấp cả positive và negative feedback một cách cân bằng.""",
            },
        ]

        response = await perplexity_ask(messages=messages)
        content = response.get("content", "Không thể tìm thấy đánh giá nhân viên")
        
        return {
            "research_results": {
                "reviews": content
            },
            "completed_searches": ["reviews"]
        }

    except Exception as e:
        return {
            "research_results": {
                "reviews": f"Lỗi khi tìm kiếm đánh giá nhân viên: {str(e)}"
            },
            "completed_searches": ["reviews"]
        }


async def search_company_competitors_market_position_node(state: CompanyResearchState) -> Dict[str, Any]:
    """Node tìm kiếm thông tin về đối thủ cạnh tranh và vị thế thị trường"""
    company_name = state["company_name"]
    try:
        messages = [
            {
                "role": "system",
                "content": "Bạn là chuyên gia phân tích thị trường và competitive intelligence. Cung cấp phân tích chi tiết bằng tiếng Việt.",
            },
            {
                "role": "user",
                "content": f"""Phân tích vị thế thị trường và cạnh tranh của công ty "{company_name}":
                - Đối thủ cạnh tranh chính (direct và indirect)
                - Market share và ranking trong ngành
                - Lợi thế cạnh tranh độc đáo
                - Thách thức từ competitors
                - Market size và growth potential
                - Target customers và segments
                - Differentiation factors
                - Threats và opportunities
                - Industry trends ảnh hưởng đến công ty
                
                Ưu tiên dữ liệu từ market research reports, industry analysis, và business news.""",
            },
        ]

        response = await perplexity_ask(messages=messages)
        content = response.get("content", "Không thể tìm thấy thông tin thị trường")
        
        return {
            "research_results": {
                "market_position": content
            },
            "completed_searches": ["market_position"]
        }

    except Exception as e:
        return {
            "research_results": {
                "market_position": f"Lỗi khi tìm kiếm thông tin thị trường: {str(e)}"
            },
            "completed_searches": ["market_position"]
        }


async def search_company_future_plans_node(state: CompanyResearchState) -> Dict[str, Any]:
    """Node tìm kiếm kế hoạch phát triển và triển vọng tương lai"""
    company_name = state["company_name"]
    try:
        messages = [
            {
                "role": "system",
                "content": "Bạn là chuyên gia strategic planning và business development. Phân tích kế hoạch tương lai doanh nghiệp bằng tiếng Việt.",
            },
            {
                "role": "user",
                "content": f"""Tìm hiểu kế hoạch phát triển và triển vọng tương lai của công ty "{company_name}":
                - Strategic roadmap 2025-2027
                - Expansion plans (geographic, product, market)
                - Upcoming product launches
                - Technology investments và digital transformation
                - Partnership và acquisition plans
                - IPO timeline (nếu applicable)
                - Hiring plans và team scaling
                - Sustainability initiatives
                - Innovation priorities
                - Leadership vision và statements
                
                Tìm thông tin từ investor presentations, CEO interviews, press releases, và strategic announcements.""",
            },
        ]

        response = await perplexity_ask(messages=messages)
        content = response.get("content", "Không thể tìm thấy thông tin kế hoạch tương lai")
        
        return {
            "research_results": {
                "future_plans": content
            },
            "completed_searches": ["future_plans"]
        }

    except Exception as e:
        return {
            "research_results": {
                "future_plans": f"Lỗi khi tìm kiếm kế hoạch tương lai: {str(e)}"
            },
            "completed_searches": ["future_plans"]
        }


async def search_company_news_mentions_node(state: CompanyResearchState) -> Dict[str, Any]:
    """Node tìm kiếm tin tức và mentions gần đây về công ty"""
    company_name = state["company_name"]
    try:
        messages = [
            {
                "role": "system",
                "content": "Bạn là chuyên gia media monitoring và public relations. Tổng hợp tin tức gần đây bằng tiếng Việt.",
            },
            {
                "role": "user",
                "content": f"""Tìm tin tức và mentions gần đây (3-6 tháng) về công ty "{company_name}":
                - Press releases và announcements
                - Media coverage và news articles
                - Awards và recognitions
                - Executive changes và promotions
                - Product launches và updates
                - Funding rounds và investments
                - Partnerships và collaborations
                - Crisis hoặc negative news (nếu có)
                - Industry event participation
                - Social media buzz và trending topics
                
                Ưu tiên nguồn tin đáng tin cậy và cập nhật nhất. Ghi rõ ngày tháng của các tin tức.""",
            },
        ]

        response = await perplexity_ask(messages=messages)
        content = response.get("content", "Không thể tìm thấy tin tức gần đây")
        
        return {
            "research_results": {
                "news": content
            },
            "completed_searches": ["news"]
        }

    except Exception as e:
        return {
            "research_results": {
                "news": f"Lỗi khi tìm kiếm tin tức: {str(e)}"
            },
            "completed_searches": ["news"]
        }


async def search_learning_opportunities_node(state: CompanyResearchState) -> Dict[str, Any]:
    """Node tìm kiếm cơ hội học hỏi và phát triển nghề nghiệp"""
    company_name = state["company_name"]
    try:
        messages = [
            {
                "role": "system",
                "content": "Bạn là chuyên gia learning & development và career coaching. Phân tích cơ hội phát triển nghề nghiệp bằng tiếng Việt.",
            },
            {
                "role": "user",
                "content": f"""Tìm hiểu cơ hội học hỏi và phát triển nghề nghiệp tại công ty "{company_name}":
                - Training programs cho fresh graduates
                - Mentorship và coaching programs
                - Internal mobility và career progression
                - Skills development opportunities
                - Conference và workshop support
                - Certification sponsorship
                - Innovation time và side projects
                - Cross-functional collaboration
                - Leadership development programs
                - Technical skill advancement
                - Industry exposure và networking
                - Performance review process
                
                Tập trung vào thông tin hữu ích cho fresh graduates và early career professionals.""",
            },
        ]

        response = await perplexity_ask(messages=messages)
        content = response.get("content", "Không thể tìm thấy thông tin học hỏi và phát triển")
        
        return {
            "research_results": {
                "learning_opportunities": content
            },
            "completed_searches": ["learning_opportunities"]
        }

    except Exception as e:
        return {
            "research_results": {
                "learning_opportunities": f"Lỗi khi tìm kiếm cơ hội học hỏi: {str(e)}"
            },
            "completed_searches": ["learning_opportunities"]
        }


# Workflow nodes
def extract_company_name(state: CompanyResearchState) -> CompanyResearchState:
    """Extract tên công ty từ tin nhắn của user"""
    last_message = state["messages"][-1]
    if isinstance(last_message, HumanMessage):
        content = last_message.content

        # Improved company name extraction
        import re

        # Patterns để tìm tên công ty
        patterns = [
            r"về công ty\s+([^.!?]+)",
            r"about company\s+([^.!?]+)",
            r"công ty\s+([A-Za-z0-9\s]+)",
            r"company\s+([A-Za-z0-9\s]+)",
            r"tìm hiểu\s+([A-Za-z0-9\s]+)",
            r"research\s+([A-Za-z0-9\s]+)",
        ]

        company_name = None
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                company_name = match.group(1).strip()
                break

        if not company_name:
            # Fallback: lấy từ sau các keyword
            words = content.split()
            keywords = ["về", "about", "công ty", "company"]
            for i, word in enumerate(words):
                if word.lower() in keywords and i + 1 < len(words):
                    company_name = " ".join(words[i + 1 : i + 3])  # Lấy 1-2 từ tiếp theo
                    break

        if not company_name:
            company_name = "Unknown Company"
    else:
        company_name = state.get("company_name", "Unknown Company")

    return {
        **state,
        "company_name": company_name,
        "search_queries": [
            "basic_info",
            "culture_benefits",
            "financial_performance",
            "reviews",
            "market_position",
            "future_plans",
            "news",
            "learning_opportunities",
        ],
        "completed_searches": [],
        "research_results": {},
    }


async def generate_report(state: CompanyResearchState) -> CompanyResearchState:
    """Node tạo báo cáo tổng hợp từ kết quả tìm kiếm sử dụng Anthropic Claude"""
    company_name = state["company_name"]
    results = state["research_results"]
    
    # Prepare research data for Claude
    research_summary = ""
    for category, content in results.items():
        research_summary += f"\n\n## {category.upper()}:\n{content}"
    
    # Use Anthropic Claude to generate comprehensive report
    if anthropic_model:
        try:
            claude_prompt = f"""
Bạn là chuyên gia phân tích doanh nghiệp và tư vấn nghề nghiệp chuyên sâu. Hãy tạo một báo cáo nghiên cứu công ty toàn diện và chuyên nghiệp về {company_name} dựa trên dữ liệu research sau:

{research_summary}

Yêu cầu tạo báo cáo bằng tiếng Việt với cấu trúc sau:

# 📊 BÁO CÁO NGHIÊN CỨU CÔNG TY: {company_name.upper()}

## 🚀 TÓM TẮT ĐIỀU HÀNH (Executive Summary)
- Tóm tắt 2-3 điểm chính về công ty
- Đánh giá tổng thể về cơ hội nghề nghiệp

## 📋 PHÂN TÍCH CHI TIẾT

### 🏢 Thông tin cơ bản
### 🎯 Văn hóa & đãi ngộ  
### 💰 Tình hình tài chính
### 💬 Đánh giá nhân viên
### 🏆 Vị thế thị trường
### 🚀 Kế hoạch tương lai
### 📰 Tin tức gần đây
### 🎓 Cơ hội phát triển

## 💡 PHÂN TÍCH CHO FRESH GRADUATE

### ✅ Điểm Mạnh (3-5 điểm cụ thể)
### ⚠️ Điểm Cần Lưu Ý (3-5 điểm cụ thể)  
### 🎯 Điểm Đánh Giá: _/10 ⭐
### 👥 Phù Hợp Với: (mô tả profile ứng viên phù hợp)

## 📝 CHUẨN BỊ PHỎNG VẤN
### Câu hỏi nên hỏi (5-7 câu cụ thể)
### Điểm nên nhấn mạnh trong CV/phỏng vấn

Hãy phân tích sâu, đưa ra insights có giá trị và recommendations cụ thể. Sử dụng dữ liệu research để đưa ra đánh giá khách quan và hữu ích cho fresh graduates.
"""

            response = await anthropic_model.ainvoke([HumanMessage(content=claude_prompt)])
            report = response.content
            
        except Exception as e:
            # Fallback to simple template if Claude fails
            report = f"""
# 📊 BÁO CÁO NGHIÊN CỨU CÔNG TY: {company_name.upper()}
*Ngày tạo: {datetime.now().strftime('%d/%m/%Y %H:%M')} | Nguồn: Perplexity AI*

---

## 🚀 TÓM TẮT ĐIỀU HÀNH (Executive Summary)

Báo cáo này tổng hợp thông tin toàn diện về **{company_name}** từ nhiều nguồn đáng tin cậy.

{research_summary}

---

## 💡 LƯU Ý
Đã xảy ra lỗi khi tạo báo cáo với AI: {str(e)}
Vui lòng kiểm tra lại cấu hình Anthropic API.

---

## 📊 METADATA
- **Searches completed**: {len(state['completed_searches'])}/8
- **Last updated**: {datetime.now().strftime('%d/%m/%Y %H:%M')}
"""
    else:
        # Fallback template if no Anthropic API
        report = f"""
# 📊 BÁO CÁO NGHIÊN CỨU CÔNG TY: {company_name.upper()}
*Ngày tạo: {datetime.now().strftime('%d/%m/%Y %H:%M')} | Nguồn: Perplexity AI*

{research_summary}

---

## 💡 LƯU Ý
Để có báo cáo phân tích chi tiết hơn, vui lòng cấu hình ANTHROPIC_API_KEY.

---

## 📊 METADATA
- **Searches completed**: {len(state['completed_searches'])}/8
- **Last updated**: {datetime.now().strftime('%d/%m/%Y %H:%M')}
"""
    
    response_message = AIMessage(content=report)
    
    return {
        **state,
        "messages": state["messages"] + [response_message]
    }


# Tạo StateGraph với parallel execution
def create_company_research_agent():
    """Tạo Company Research Agent sử dụng LangGraph với parallel nodes"""

    # Tạo graph
    workflow = StateGraph(CompanyResearchState)

    # Thêm nodes
    workflow.add_node("extract_company", extract_company_name)
    
    # Add all research nodes 
    workflow.add_node("basic_info", search_company_basic_info_node)
    workflow.add_node("culture_benefits", search_company_culture_benefits_node)
    workflow.add_node("financial_performance", search_company_financial_performance_node)
    workflow.add_node("reviews", search_company_reviews_glassdoor_node)
    workflow.add_node("market_position", search_company_competitors_market_position_node)
    workflow.add_node("future_plans", search_company_future_plans_node)
    workflow.add_node("news", search_company_news_mentions_node)
    workflow.add_node("learning_opportunities", search_learning_opportunities_node)
    
    workflow.add_node("generate_report", generate_report)

    # Define parallel execution flow
    # All research nodes run in parallel after extract_company
    workflow.add_edge(START, "extract_company")
    
    # All research nodes start from extract_company
    workflow.add_edge("extract_company", "basic_info")
    workflow.add_edge("extract_company", "culture_benefits")
    workflow.add_edge("extract_company", "financial_performance")
    workflow.add_edge("extract_company", "reviews")
    workflow.add_edge("extract_company", "market_position")
    workflow.add_edge("extract_company", "future_plans")
    workflow.add_edge("extract_company", "news")
    workflow.add_edge("extract_company", "learning_opportunities")
    
    # All research nodes flow to generate_report
    workflow.add_edge("basic_info", "generate_report")
    workflow.add_edge("culture_benefits", "generate_report")
    workflow.add_edge("financial_performance", "generate_report")
    workflow.add_edge("reviews", "generate_report")
    workflow.add_edge("market_position", "generate_report")
    workflow.add_edge("future_plans", "generate_report")
    workflow.add_edge("news", "generate_report")
    workflow.add_edge("learning_opportunities", "generate_report")
    
    workflow.add_edge("generate_report", END)

    # Compile graph
    return workflow.compile()


async def research_company_async(company_name: str) -> Dict[str, Any]:
    """
    Research a company asynchronously
    
    Args:
        company_name: Name of the company to research
        
    Returns:
        Dict containing the research results and report
    """
    # Validate API keys
    if PERPLEXITY_API_KEY == "your_perplexity_api_key_here":
        raise ValueError("PERPLEXITY_API_KEY not configured. Please set the environment variable.")
    
    # Check Anthropic API (optional)
    if ANTHROPIC_API_KEY == "your_anthropic_api_key_here":
        print("⚠️ ANTHROPIC_API_KEY not configured - using basic report template")
    
    print(f"🔍 Researching company: {company_name}")
    print("⚡ Running parallel research with 8 data sources...")
    
    # Create agent
    agent = create_company_research_agent()

    # Prepare input
    input_data = {
        "messages": [
            HumanMessage(content=f"Research company {company_name} for job application preparation")
        ],
        "company_name": company_name,
        "research_results": {},
        "search_queries": [],
        "completed_searches": [],
    }

    try:
        # Execute research
        result = await agent.ainvoke(input_data)
        
        print(f"✅ Research completed: {len(result['completed_searches'])}/8 searches")
        print(f"🏢 Company analyzed: {result['company_name']}")
        
        return {
            "company_name": result["company_name"],
            "research_results": result["research_results"], 
            "completed_searches": result["completed_searches"],
            "report": result["messages"][-1].content,
            "success": True
        }

    except Exception as e:
        print(f"❌ Error during research: {str(e)}")
        return {
            "company_name": company_name,
            "error": str(e),
            "success": False
        }

def research_company(company_name: str) -> Dict[str, Any]:
    """
    Synchronous wrapper to research a company
    
    Args:
        company_name: Name of the company to research
        
    Returns:
        Dict containing the research results and report
    """
    return asyncio.run(research_company_async(company_name))


def main():
    """Main function for CLI usage"""
    import sys
    
    print("🚀 COMPANY RESEARCH AGENT")
    print("=" * 50)
    
    # Get company name from command line or user input
    if len(sys.argv) > 1:
        company_name = " ".join(sys.argv[1:])
    else:
        company_name = input("Enter company name to research: ").strip()
    
    if not company_name:
        print("❌ Company name is required")
        return
    
    try:
        # Run research
        result = research_company(company_name)
        
        if result["success"]:
            print("\n" + "=" * 70)
            print("📊 COMPANY RESEARCH REPORT")
            print("=" * 70)
            print(result["report"])
            
            # Optionally save to file
            save_option = input("\n💾 Save report to file? (y/n): ").strip().lower()
            if save_option == 'y':
                filename = f"{company_name.replace(' ', '_')}_research_report.md"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(result["report"])
                print(f"✅ Report saved to {filename}")
        else:
            print(f"❌ Research failed: {result['error']}")
    
    except KeyboardInterrupt:
        print("\n\n⏹️ Research interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")


if __name__ == "__main__":
    main()
