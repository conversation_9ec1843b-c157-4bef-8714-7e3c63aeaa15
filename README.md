# Company Research Agent

A powerful AI-powered tool that researches companies using Perplexity AI and generates comprehensive reports with Anthropic Claude.

## Features

- **Parallel Research**: 8 concurrent data sources for comprehensive analysis
- **AI-Powered Reports**: <PERSON> generates insightful, structured reports
- **Multiple Usage Modes**: CLI, programmatic, and batch processing
- **Fast Execution**: ~30 seconds vs traditional 2-3 minutes
- **Professional Output**: Structured markdown reports with actionable insights

## Quick Start

### 1. Setup

```bash
# Install dependencies
pip install langgraph langchain langchain-anthropic httpx python-dotenv

# Set up API keys
export PERPLEXITY_API_KEY="your_perplexity_api_key"
export ANTHROPIC_API_KEY="your_anthropic_api_key"  # Optional but recommended
```

### 2. CLI Usage

```bash
# Run with company name as argument
python agent.py "Google"

# Or run interactively
python agent.py
# Enter company name: Google
```

### 3. Programmatic Usage

```python
from agent import research_company

# Research a company
result = research_company("Microsoft")

if result["success"]:
    print(result["report"])
    
    # Save to file
    with open("microsoft_report.md", "w") as f:
        f.write(result["report"])
else:
    print(f"Error: {result['error']}")
```

### 4. Async Usage

```python
import asyncio
from agent import research_company_async

async def research_multiple():
    companies = ["Apple", "Google", "Microsoft"]
    tasks = [research_company_async(name) for name in companies]
    results = await asyncio.gather(*tasks)
    return results

# Run async research
results = asyncio.run(research_multiple())
```

## API Keys

### Required: Perplexity AI
1. Sign up at [perplexity.ai](https://www.perplexity.ai/)
2. Get API key from settings
3. Set `PERPLEXITY_API_KEY` environment variable

### Optional: Anthropic Claude
1. Sign up at [console.anthropic.com](https://console.anthropic.com/)
2. Get API key from settings  
3. Set `ANTHROPIC_API_KEY` environment variable

*Note: Without Anthropic API, you'll get basic reports. With it, you get AI-analyzed insights.*

## Report Structure

Generated reports include:

- **Executive Summary**: Key insights and opportunities
- **Detailed Analysis**: 8 comprehensive sections
  - Company basics & overview
  - Culture & benefits
  - Financial performance  
  - Employee reviews
  - Market position
  - Future plans
  - Recent news
  - Learning opportunities
- **Fresh Graduate Analysis**: Pros, cons, fit assessment
- **Interview Preparation**: Questions and tips

## Examples

See `example_usage.py` for detailed examples including:
- Single company research
- Batch processing multiple companies
- Async concurrent research
- File saving and organization

## Performance

- **Execution Time**: ~30 seconds (parallel processing)
- **Data Sources**: 8 concurrent research queries
- **Output Quality**: Professional, actionable insights
- **Success Rate**: High reliability with error handling

## Error Handling

The agent gracefully handles:
- API key validation
- Network connectivity issues
- Individual research failures
- Rate limiting and timeouts

## License

MIT License - feel free to use and modify as needed.